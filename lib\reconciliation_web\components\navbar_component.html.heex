<header class="w-5/6 mx-auto text-gray-800 p-4 mt-8 flex justify-between items-center shadow rounded-lg border-t border-orange-500" style="background: rgba(249, 250, 251, 0.95); backdrop-filter: blur(20px);">

  <!-- System Status -->
  <div class="flex items-center justify-between">
    <div class="flex items-center mr-6">
      <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      <span class="ml-2 text-sm font-medium text-gray-800">System Online</span>
    </div>
    <div class="text-sm text-gray-600 mr-6">
      <%= Date.utc_today() |> Calendar.strftime("%b %d") %>
    </div>
  </div>

  <!-- User Info -->
  <div class="flex items-center">
    <%= if @current_user do %>
      <span class="text-gray-800 mr-4"><%= @current_user.email %></span>
      <.form for={%{}} action={~p"/users/log_out"} method="delete" class="inline-block">
        <button type="submit" class="px-3 py-1 bg-orange-400 rounded hover:bg-orange-500 text-white transition-colors duration-200">Logout</button>
      </.form>
    <% end %>
  </div>
</header>