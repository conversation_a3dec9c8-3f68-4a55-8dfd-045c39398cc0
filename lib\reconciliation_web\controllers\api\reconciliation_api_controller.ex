defmodule ReconciliationWeb.Api.ReconciliationApiController do
  use ReconciliationWeb, :controller

  alias Reconciliation.Services.{TransactionService, ActivityLogger}

  @doc """
  API endpoint to get user's reconciliation runs
  """
  def index(conn, params) do
    user = conn.assigns.current_user
    
    # Parse pagination parameters
    page = Map.get(params, "page", "1") |> String.to_integer()
    per_page = Map.get(params, "per_page", "20") |> String.to_integer() |> min(100)
    
    # Get reconciliation runs with pagination
    runs = Reconciliation.list_reconciliation_runs(user.id)
    total_count = length(runs)
    
    # Apply pagination
    offset = (page - 1) * per_page
    paginated_runs = runs |> Enum.drop(offset) |> Enum.take(per_page)
    
    # Format response
    formatted_runs = Enum.map(paginated_runs, fn run ->
      %{
        id: run.id,
        name: run.name,
        status: run.status,
        created_at: run.inserted_at,
        updated_at: run.updated_at,
        files_count: length(run.uploaded_files || [])
      }
    end)

    # Log API access
    ActivityLogger.log_data_access_activity(
      user.id,
      "api_access",
      organization_id: user.organization_id,
      metadata: %{
        endpoint: "reconciliation_runs",
        page: page,
        per_page: per_page,
        results_count: length(formatted_runs)
      }
    )

    conn
    |> json(%{
      success: true,
      data: formatted_runs,
      pagination: %{
        page: page,
        per_page: per_page,
        total_count: total_count,
        total_pages: ceil(total_count / per_page)
      }
    })
  end

  @doc """
  API endpoint to get specific reconciliation run
  """
  def show(conn, %{"id" => run_id}) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      run = Reconciliation.get_user_reconciliation_run(run_id_int, user.id)

      case run do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "Reconciliation run not found"})

        run ->
          # Get additional details
          uploaded_files = Reconciliation.get_uploaded_files(run.id)
          transactions_count = length(Reconciliation.get_transactions(run.id))
          matches_count = length(Reconciliation.get_transaction_matches(run.id))

          formatted_run = %{
            id: run.id,
            name: run.name,
            status: run.status,
            created_at: run.inserted_at,
            updated_at: run.updated_at,
            files: Enum.map(uploaded_files, fn file ->
              %{
                id: file.id,
                filename: file.filename,
                original_filename: file.original_filename,
                file_type: file.file_type,
                file_size: file.file_size,
                status: file.status
              }
            end),
            statistics: %{
              transactions_count: transactions_count,
              matches_count: matches_count
            }
          }

          # Log API access
          ActivityLogger.log_data_access_activity(
            user.id,
            "api_access",
            organization_id: user.organization_id,
            metadata: %{
              endpoint: "reconciliation_run_details",
              run_id: run_id_int,
              run_name: run.name
            }
          )

          conn
          |> json(%{
            success: true,
            data: formatted_run
          })
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end

  @doc """
  API endpoint to get transactions for a reconciliation run
  """
  def transactions(conn, %{"id" => run_id} = params) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      run = Reconciliation.get_user_reconciliation_run(run_id_int, user.id)

      case run do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "Reconciliation run not found"})

        _run ->
          # Parse pagination and filter parameters
          page = Map.get(params, "page", "1") |> String.to_integer()
          per_page = Map.get(params, "per_page", "50") |> String.to_integer() |> min(200)
          
          # Get transactions
          all_transactions = Reconciliation.get_transactions(run_id_int)
          
          # Apply filters if provided
          filtered_transactions = TransactionService.apply_filters(all_transactions, %{
            selected_date: params["date"],
            selected_status: params["status"],
            selected_type: params["type"],
            search_query: params["search"]
          })
          
          total_count = length(filtered_transactions)
          
          # Apply pagination
          offset = (page - 1) * per_page
          paginated_transactions = filtered_transactions |> Enum.drop(offset) |> Enum.take(per_page)
          
          # Format transactions
          formatted_transactions = Enum.map(paginated_transactions, fn transaction ->
            %{
              id: transaction.id,
              reference: transaction.reference,
              description: transaction.description,
              amount: transaction.amount,
              currency: transaction.currency,
              transaction_date: transaction.transaction_date,
              is_matched: transaction.is_matched,
              file_source: transaction.uploaded_file.file_type,
              created_at: transaction.inserted_at
            }
          end)

          # Log API access
          ActivityLogger.log_data_access_activity(
            user.id,
            "api_access",
            organization_id: user.organization_id,
            metadata: %{
              endpoint: "reconciliation_transactions",
              run_id: run_id_int,
              page: page,
              per_page: per_page,
              results_count: length(formatted_transactions),
              filters_applied: Map.take(params, ["date", "status", "type", "search"])
            }
          )

          conn
          |> json(%{
            success: true,
            data: formatted_transactions,
            pagination: %{
              page: page,
              per_page: per_page,
              total_count: total_count,
              total_pages: ceil(total_count / per_page)
            }
          })
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end

  @doc """
  API endpoint to get transaction summary
  """
  def summary(conn, %{"id" => run_id}) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      run = Reconciliation.get_user_reconciliation_run(run_id_int, user.id)

      case run do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "Reconciliation run not found"})

        _run ->
          summary = TransactionService.calculate_transaction_totals(user.id, run_id_int)

          # Log API access
          ActivityLogger.log_data_access_activity(
            user.id,
            "api_access",
            organization_id: user.organization_id,
            metadata: %{
              endpoint: "reconciliation_summary",
              run_id: run_id_int
            }
          )

          conn
          |> json(%{
            success: true,
            data: summary
          })
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end
end
