defmodule ReconciliationWeb.ReconciliationController do
  use ReconciliationWeb, :controller

  alias Reconciliation.Services.{TransactionService, FileProcessingService, ActivityLogger}
  alias Reconciliation.Services.ExcelExport

  @doc """
  Run matching engine for a reconciliation run
  """
  def run_matching(conn, %{"run_id" => run_id}) do
    user = conn.assigns.current_user

    case TransactionService.run_matching_engine(run_id, user.id) do
      {:ok, matches} ->
        conn
        |> put_flash(:info, "Matching completed successfully! Found #{length(matches)} matches.")
        |> json(%{
          success: true,
          message: "Matching completed successfully",
          matches_count: length(matches)
        })

      {:error, error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          success: false,
          error: error
        })
    end
  end

  @doc """
  Export transactions to Excel
  """
  def export_transactions(conn, %{"run_id" => run_id, "format" => format}) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      run = Reconciliation.get_reconciliation_run!(run_id_int)

      # Get transactions for the run
      transactions = Reconciliation.get_transactions(run_id_int)

      case format do
        "excel" ->
          case ExcelExport.generate_transactions_excel_from_template(transactions, run.name) do
            {:ok, file_path, filename} ->
              # Log the export activity
              ActivityLogger.log_reconciliation_activity(
                user.id,
                "export_data",
                resource_type: "reconciliation_run",
                resource_id: run_id_int,
                organization_id: user.organization_id,
                metadata: %{
                  export_type: "excel",
                  filename: filename,
                  transaction_count: length(transactions),
                  run_name: run.name
                }
              )

              conn
              |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
              |> put_resp_header("content-disposition", "attachment; filename=\"#{filename}\"")
              |> send_file(200, file_path)

            {:error, error} ->
              conn
              |> put_status(:unprocessable_entity)
              |> json(%{success: false, error: error})
          end

        "csv" ->
          # CSV export logic would go here
          conn
          |> put_status(:not_implemented)
          |> json(%{success: false, error: "CSV export not yet implemented"})

        _ ->
          conn
          |> put_status(:bad_request)
          |> json(%{success: false, error: "Unsupported format"})
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end

  @doc """
  Process uploaded file
  """
  def process_file(conn, %{"file_id" => file_id}) do
    user = conn.assigns.current_user

    try do
      uploaded_file = Reconciliation.get_uploaded_file!(file_id)
      
      # Verify user owns this file
      unless uploaded_file.reconciliation_run.user_id == user.id do
        conn
        |> put_status(:forbidden)
        |> json(%{success: false, error: "Access denied"})
      else
        # Start file processing
        Task.start(fn ->
          FileProcessingService.process_file_with_progress(
            uploaded_file, 
            uploaded_file.reconciliation_run_id
          )
        end)

        conn
        |> json(%{
          success: true,
          message: "File processing started",
          file_id: file_id
        })
      end

    rescue
      Ecto.NoResultsError ->
        conn
        |> put_status(:not_found)
        |> json(%{success: false, error: "File not found"})

      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end

  @doc """
  Get reconciliation run status
  """
  def run_status(conn, %{"run_id" => run_id}) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      run = Reconciliation.get_user_reconciliation_run(run_id_int, user.id)

      case run do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "Reconciliation run not found"})

        run ->
          # Get additional status information
          uploaded_files = Reconciliation.get_uploaded_files(run.id)
          transactions_count = length(Reconciliation.get_transactions(run.id))
          matches_count = length(Reconciliation.get_transaction_matches(run.id))

          conn
          |> json(%{
            success: true,
            run: %{
              id: run.id,
              name: run.name,
              status: run.status,
              created_at: run.inserted_at,
              updated_at: run.updated_at,
              files_count: length(uploaded_files),
              transactions_count: transactions_count,
              matches_count: matches_count
            }
          })
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end

  @doc """
  Get transaction summary for a run
  """
  def transaction_summary(conn, %{"run_id" => run_id}) do
    user = conn.assigns.current_user

    try do
      run_id_int = String.to_integer(run_id)
      
      # Verify user owns this run
      run = Reconciliation.get_user_reconciliation_run(run_id_int, user.id)
      
      case run do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "Reconciliation run not found"})

        _run ->
          summary = TransactionService.calculate_transaction_totals(user.id, run_id_int)

          conn
          |> json(%{
            success: true,
            summary: summary
          })
      end

    rescue
      error ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: Exception.message(error)})
    end
  end
end
